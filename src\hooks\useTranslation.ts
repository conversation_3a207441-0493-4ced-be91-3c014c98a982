import { useState, useEffect } from 'react'
import { translations, Language, TranslationKey } from '@/lib/translations'

interface TranslationCache {
  [key: string]: { [lang: string]: string }
}

// Função para buscar tradução estática como fallback
function getStaticTranslation(text: string, targetLang: string): string | null {
  const textLower = text.toLowerCase().trim()

  // Procurar por tradução exata nas traduções estáticas
  for (const [key, value] of Object.entries(translations.pt)) {
    if (value.toLowerCase() === textLower) {
      return translations[targetLang as Language]?.[key] || null
    }
  }

  // Procurar por correspondência parcial para frases comuns
  const commonMappings: Record<string, Record<string, string>> = {
    'marketplace': { en: 'Marketplace', es: 'Marketplace' },
    'central de ajuda': { en: 'Help Center', es: 'Centro de Ayuda' },
    'contactos': { en: 'Contacts', es: 'Contactos' },
    'sobre nós': { en: 'About Us', es: 'Sobre Nosotros' },
    'entrar': { en: 'Login', es: 'Iniciar Sesión' },
    'registar': { en: 'Register', es: 'Registrarse' },
    'cliente': { en: 'Customer', es: 'Cliente' },
    'técnico': { en: 'Technician', es: 'Técnico' },
    'reparações': { en: 'Repairs', es: 'Reparaciones' },
    'dispositivos': { en: 'Devices', es: 'Dispositivos' },
    'eletrónicos': { en: 'Electronics', es: 'Electrónicos' },
    'serviços': { en: 'Services', es: 'Servicios' },
    'produtos': { en: 'Products', es: 'Productos' }
  }

  return commonMappings[textLower]?.[targetLang] || null
}

export function useTranslation() {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('pt')
  const [translationCache, setTranslationCache] = useState<TranslationCache>({})
  const [isTranslating, setIsTranslating] = useState(false)

  useEffect(() => {
    // Detectar idioma do navegador
    const detectLanguage = () => {
      const browserLang = navigator.language.toLowerCase()

      if (browserLang.startsWith('en')) return 'en'
      if (browserLang.startsWith('es')) return 'es'
      return 'pt' // Default para português
    }

    // Verificar se há idioma salvo no localStorage
    const savedLanguage = localStorage.getItem('revify-language') as Language
    if (savedLanguage && translations[savedLanguage]) {
      setCurrentLanguage(savedLanguage)
    } else {
      // Detectar automaticamente
      const detectedLang = detectLanguage()
      setCurrentLanguage(detectedLang)
      localStorage.setItem('revify-language', detectedLang)
    }
  }, [])

  const translate = async (text: string, targetLang: string = currentLanguage): Promise<string> => {
    // Se o idioma é português ou o texto está vazio, retornar original
    if (targetLang === 'pt' || !text.trim()) {
      return text
    }

    // Verificar cache local primeiro
    const cacheKey = text.toLowerCase().trim()
    if (translationCache[cacheKey] && translationCache[cacheKey][targetLang]) {
      return translationCache[cacheKey][targetLang]
    }

    // Verificar cache do localStorage para persistência
    const localCacheKey = `translation_${cacheKey}_${targetLang}`
    const cachedTranslation = localStorage.getItem(localCacheKey)
    if (cachedTranslation) {
      // Salvar no cache em memória também
      if (!translationCache[cacheKey]) {
        translationCache[cacheKey] = {}
      }
      translationCache[cacheKey][targetLang] = cachedTranslation
      return cachedTranslation
    }

    try {
      setIsTranslating(true)
      const response = await fetch('/api/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text,
          targetLang,
          sourceLang: 'pt'
        })
      })

      if (response.ok) {
        const data = await response.json()
        const translatedText = data.translatedText

        // Salvar no cache em memória
        setTranslationCache(prev => ({
          ...prev,
          [cacheKey]: {
            ...prev[cacheKey],
            [targetLang]: translatedText
          }
        }))

        // Salvar no localStorage para persistência
        localStorage.setItem(localCacheKey, translatedText)

        return translatedText
      } else {
        console.error('Erro na tradução:', response.status)

        // Se for erro 429 (rate limit), tentar usar traduções estáticas como fallback
        if (response.status === 429) {
          const staticTranslation = getStaticTranslation(text, targetLang)
          if (staticTranslation) {
            // Salvar no cache também
            setTranslationCache(prev => ({
              ...prev,
              [cacheKey]: {
                ...prev[cacheKey],
                [targetLang]: staticTranslation
              }
            }))
            localStorage.setItem(localCacheKey, staticTranslation)
            return staticTranslation
          }
        }

        return text
      }
    } catch (error) {
      console.error('Erro na tradução:', error)
      return text
    } finally {
      setIsTranslating(false)
    }
  }

  // Função universal de tradução - usa DeepL para tudo
  const t = async (text: string): Promise<string> => {
    // Se for português ou texto vazio, retornar original
    if (currentLanguage === 'pt' || !text.trim()) {
      return text
    }

    // Verificar cache primeiro
    const cacheKey = text.toLowerCase().trim()
    if (translationCache[cacheKey] && translationCache[cacheKey][currentLanguage]) {
      return translationCache[cacheKey][currentLanguage]
    }

    // Traduzir via DeepL
    return await translate(text, currentLanguage)
  }

  // Função síncrona para textos já traduzidos (em cache)
  const tSync = (text: string): string => {
    if (currentLanguage === 'pt') return text

    const cacheKey = text.toLowerCase().trim()
    return translationCache[cacheKey]?.[currentLanguage] || text
  }

  // Função para traduzir múltiplos textos de uma vez (mais eficiente)
  const tBatch = async (texts: string[]): Promise<string[]> => {
    if (currentLanguage === 'pt') return texts

    const results: string[] = []
    const textsToTranslate: { index: number; text: string }[] = []

    // Verificar cache para cada texto
    texts.forEach((text, index) => {
      const cacheKey = text.toLowerCase().trim()
      if (translationCache[cacheKey] && translationCache[cacheKey][currentLanguage]) {
        results[index] = translationCache[cacheKey][currentLanguage]
      } else {
        textsToTranslate.push({ index, text })
      }
    })

    // Traduzir textos que não estão em cache
    if (textsToTranslate.length > 0) {
      const translationPromises = textsToTranslate.map(async ({ index, text }) => {
        const translated = await translate(text, currentLanguage)
        results[index] = translated
        return translated
      })

      await Promise.all(translationPromises)
    }

    return results
  }

  const changeLanguage = async (newLang: Language) => {
    setCurrentLanguage(newLang)
    localStorage.setItem('revify-language', newLang)

    // Se não for português, pré-carregar traduções comuns
    if (newLang !== 'pt') {
      const commonTexts = [
        'Reparações Rápidas e Confiáveis',
        'Encontre especialistas próximos de si para reparar o seu dispositivo',
        'Solicitar Reparação',
        'Qual é o problema?',
        'Localização',
        'Continuar Reparação',
        'Reparamos todos os dispositivos',
        'Como funciona',
        'Porquê escolher Revify?',
        'Marketplace',
        'Central de Ajuda',
        'Contactos',
        'Sobre Nós',
        'Entrar',
        'Registar',
        'Dashboard',
        'Produtos',
        'Preço',
        'Stock',
        'Status',
        'Ações',
        'Nome',
        'Descrição',
        'Categoria',
        'Marca',
        'Ativo',
        'Inativo',
        'Salvar',
        'Cancelar',
        'Editar',
        'Eliminar'
      ]

      // Traduzir em lotes para ser mais eficiente
      await tBatch(commonTexts)
    }
  }

  return {
    currentLanguage,
    changeLanguage,
    translate,
    t,
    tSync,
    tBatch,
    isTranslating
  }
}

// Hook para detectar localização geográfica
export function useGeolocation() {
  const [country, setCountry] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const detectCountry = async () => {
      try {
        // Usar API gratuita para detectar país
        const response = await fetch('https://ipapi.co/json/')
        const data = await response.json()

        if (data.country_code) {
          setCountry(data.country_code.toLowerCase())

          // Ajustar idioma baseado no país
          const countryToLanguage: Record<string, Language> = {
            'pt': 'pt',
            'br': 'pt',
            'es': 'es',
            'mx': 'es',
            'ar': 'es',
            'co': 'es',
            'pe': 'es',
            'cl': 'es',
            'us': 'en',
            'gb': 'en',
            'ca': 'en',
            'au': 'en',
          }

          const suggestedLang = countryToLanguage[data.country_code.toLowerCase()] || 'en'

          // Só mudar se não houver idioma salvo
          if (!localStorage.getItem('revify-language')) {
            localStorage.setItem('revify-language', suggestedLang)
            window.location.reload() // Recarregar para aplicar o idioma
          }
        }
      } catch (error) {
        console.log('Erro ao detectar localização:', error)
      } finally {
        setLoading(false)
      }
    }

    detectCountry()
  }, [])

  return { country, loading }
}

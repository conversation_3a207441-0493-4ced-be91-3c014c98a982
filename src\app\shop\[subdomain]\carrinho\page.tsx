'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLef<PERSON>, Minus, Plus, Trash2 } from 'lucide-react'
import ShopLayout from '@/components/shop/ShopLayout'
import { useToast, showSuccessToast, showErrorToast } from '@/components/ui/Toast'
import { useShopCart } from '@/hooks/useShopCart'
interface Product {
  id: string
  name: string
  price: number
  images: string[]
  stock: number
  brand?: {
    name: string}
}

interface CartItem {
  id: string
  product: Product
  quantity: number
  price: number
  stock: number
  name: string
  images: string[]
  brand?: {
    name: string}
}

interface ShopData {
  id: string
  name: string
  companyName?: string
  phone?: string
  email?: string
  address?: string
  city?: string
  postalCode?: string
  logoUrl?: string}

function CarrinhoContent() {
  const params = useParams()
  const { addToast } = useToast()
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [shopConfig, setShopConfig] = useState<any>(null)

  const subdomain = params.subdomain as string
  const { cart, updateQuantity: updateCartQuantity, removeFromCart, clearCart: clearCartHook } = useShopCart(subdomain)

  useEffect(() => {
    fetchShopConfig()
    if (Object.keys(cart).length > 0) {
      fetchCartItems()
    } else {
      setCartItems([])
      setIsLoading(false)
    }
  }, [cart])

  const fetchShopConfig = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/config`)
      if (response.ok) {
        const data = await response.json()
        setShopConfig(data)
      }
    } catch (error) {
      console.error('Erro ao buscar configurações da loja:', 'error')
    }
  }

  const fetchCartItems = async () => {
    try {
      const productIds = Object.keys(cart)
      console.log('Fetching cart items for IDs:', 'productIds') // Debug

      if (productIds.length === 0) {
        setCartItems([])
        setIsLoading(false)
        return
      }

      const response = await fetch(`/api/shop/${subdomain}/produtos?ids=${productIds.join(,)
}`)
      console.log('Cart API response status:', response.status) // Debug

      if (response.ok) {
        const data = await response.json()
        console.log(Cart API response data: , data) // Debug

        const items: CartItem[] = data.products.map((product: Product) => ({
          ...product,
          quantity: cart[product.id]
        
}))
        console.log('Formatted cart items:', 'items') // Debug
        setCartItems(items)
      } else {
        console.error(Failed to fetch cart items:, response.status, await response.text())
      
}
    } catch (error) {
      console.error('Erro ao buscar produtos do carrinho:', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId)
      showSuccessToast(addToast, 'Produto removido', 'Produto removido do carrinho')
    } else {
      updateCartQuantity(productId, 'newQuantity')
      showSuccessToast(addToast, 'Quantidade atualizada')
    }
  }

  const removeItem = (productId: string) => {
    removeFromCart(productId)
    showSuccessToast(addToast, 'Produto removido', 'Produto removido do carrinho')
  }

  const clearCart = () => {
    clearCartHook()
    showSuccessToast(addToast, 'Carrinho limpo', 'Todos os produtos foram removidos')
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-PT', {
      style: currency,
      currency: EUR}).format(price)
  }

  const getSubtotal = () => {
    return cartItems.reduce((total, 'item') => total + (item.price * item.quantity), 0)
  }

  const getShipping = () => {
    const subtotal = getSubtotal()
    const freeShippingThreshold = shopConfig?.freeShippingThreshold || 50
    const defaultShippingRate = shopConfig?.defaultShippingRate || 5.99
    return subtotal >= freeShippingThreshold ? 0 : 'defaultShippingRate'}

  const getFreeShippingThreshold = () => {
    return shopConfig?.freeShippingThreshold || 50
  }

  const getTotal = () => {
    return getSubtotal() + getShipping()
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Carrinho de Compras</h1>
          <p className="text-gray-600 mt-2">
            {cartItems.length} {cartItems.length === 1 ? 'item' : 'itens'} no seu carrinho
          </p>
        </div>

        {cartItems.length === 0 ? (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-medium text-gray-900 mb-2">O seu carrinho está vazio</h3>
              <p className="text-gray-500 mb-6">Adicione alguns produtos para começar as suas compras.</p>
              <Link
                href={`/shop/${subdomain}/produtos`}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Continuar Comprando
              </Link>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-900">Itens do Carrinho</h3>
                    <button
                      onClick={clearCart}
                      className="text-sm text-red-600 hover:text-red-800"
                    >Limpar Carrinho</button>
                  </div>
                </div>

                <div className="divide-y divide-gray-200">
                  {cartItems.map((item) => (
                    <div key={item.id} className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0 w-20 h-20">
                          <img
                            src={item.images[0] || '/images/placeholder-product.jpg'}
                            alt={item.name}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        </div>

                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {item.name}
                          </h4>
                          <p className="text-sm text-gray-500">
                            {item.brand?.name}
                          </p>
                          <p className="text-sm font-medium text-gray-900 mt-1">
                            {formatPrice(item.price)}
                          </p>
                          <p className="text-xs text-gray-500">
                            Stock: {item.stock} unidades
                          </p>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="p-1 text-gray-400 hover:text-gray-600"
                          >
                            <Minus className="w-4 h-4" />
                          </button>
                          <span className="w-8 text-center text-sm font-medium">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            disabled={item.quantity >= item.stock}
                            className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                        </div>

                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">
                            {formatPrice(item.price * item.quantity)}
                          </p>
                          <button
                            onClick={() => removeItem(item.id)}
                            className="text-sm text-red-600 hover:text-red-800 mt-1"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo do Pedido</h3>

                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium">{formatPrice(getSubtotal())}</span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Envio</span>
                    <span className="font-medium">
                      {getShipping() === 0 ? 'Grátis' : formatPrice(getShipping())}
                    </span>
                  </div>

                  {getShipping() === 0 && getSubtotal() >= getFreeShippingThreshold() && (
                    <div className="text-xs text-green-600">🎉 Parabéns! Portes grátis aplicados</div>
                  )}

                  {getShipping() > 0 && (
                    <div className="text-xs text-gray-500">
                      Faltam {formatPrice(getFreeShippingThreshold() - getSubtotal())} para portes grátis
                    </div>
                  )}

                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between">
                      <span className="text-base font-semibold text-gray-900">Total</span>
                      <span className="text-base font-semibold text-gray-900">
                        {formatPrice(getTotal())}
                      </span>
                    </div>
                  </div>
                </div>

                <Link
                  href={`/shop/${subdomain}/checkout`}
                  className="w-full mt-6 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-gray-900 to-black text-white rounded-lg hover:from-black hover:to-gray-900"
                >Finalizar Compra</Link>

                <Link
                  href={`/shop/${subdomain}/produtos`}
                  className="w-full mt-3 flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                >
                  Continuar Comprando
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
  )
}

export default function CarrinhoPage() {
  const params = useParams()
  const [shop, setShop] = useState<ShopData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const subdomain = params.subdomain as string

  // Hook para gerenciar carrinho
  const { getCartItemCount } = useShopCart(subdomain)

  useEffect(() => {
    if (subdomain) {
      fetchShopData()
    }
  }, [subdomain])

  const fetchShopData = async () => {
    try {
      const response = await fetch(`/api/shop/${subdomain}/config`)
      if (response.ok) {
        const data = await response.json()
        setShop(data)
      }
    } catch (error) {
      console.error(Erro ao carregar dados da loja:, 'error')
    
} finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!shop) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loja não encontrada</h1>
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800"
          >Voltar ao início</Link>
        </div>
      </div>
    )
  }

  return (
    <ShopLayout
      shopName={shop.companyName || shop.name}
      subdomain={subdomain}
      logoUrl={shop.logoUrl}
      cartItemsCount={getCartItemCount()}
      shopInfo={{
        phone: shop.phone,
        address: shop.address,
        city: shop.city,
        postalCode: shop.postalCode
      }}
    >
      <CarrinhoContent />
    </ShopLayout>
  )
}
